import {
  ArrowUp,
  Calendar,
  CheckCircle,
  Clock,
  Eye,
  List,
  UserCheck,
  Users,
  XCircle,
} from "lucide-react";
import { useMemo, useState } from "react";
import SuperUserTicketDetailModal from "../components/superuser/SuperUserTicketDetailModal";
import { filterOptions, filterTickets, getTicketsByView } from "../data/data";

const SuperUserDashboard = () => {
  // View management
  const [activeView, setActiveView] = useState("all"); // all, unapproved, myTickets

  // Selection and modal state
  const [selectedTickets, setSelectedTickets] = useState([]);
  const [showApprovalModal, setShowApprovalModal] = useState(false);
  const [selectedTicket, setSelectedTicket] = useState(null);

  // Filter state - separate for each view
  const [appliedFilters, setAppliedFilters] = useState({
    all: {},
    unapproved: {},
    myTickets: {},
  });

  // Multi-select filter management functions
  const handleApplyFilters = (filters) => {
    setAppliedFilters((prev) => ({
      ...prev,
      [activeView]: filters,
    }));
  };

  const handleClearFilters = () => {
    setAppliedFilters((prev) => ({
      ...prev,
      [activeView]: {},
    }));
  };

  // Get tickets for current view and apply filters
  const currentViewTickets = useMemo(() => {
    const viewTickets = getTicketsByView(activeView);
    const currentFilters = appliedFilters[activeView] || {};
    return filterTickets(viewTickets, currentFilters);
  }, [activeView, appliedFilters]);

  const getPriorityColor = (priority) => {
    switch (priority) {
      case "Critical":
        return "bg-red-500";
      case "High":
        return "bg-orange-500";
      case "Medium":
        return "bg-yellow-500";
      case "Low":
        return "bg-green-500";
      default:
        return "bg-gray-500";
    }
  };

  const getRiskColor = (risk) => {
    switch (risk) {
      case "High":
        return "text-red-600 bg-red-100";
      case "Medium":
        return "text-yellow-600 bg-yellow-100";
      case "Low":
        return "text-green-600 bg-green-100";
      default:
        return "text-gray-600 bg-gray-100";
    }
  };

  const getCategoryColor = (category) => {
    switch (category) {
      case "Technical":
        return "text-blue-700 bg-blue-100 border-blue-200";
      case "Feature Request":
        return "text-purple-700 bg-purple-100 border-purple-200";
      case "Security":
        return "text-red-700 bg-red-100 border-red-200";
      case "Documentation":
        return "text-green-700 bg-green-100 border-green-200";
      case "Billing":
        return "text-orange-700 bg-orange-100 border-orange-200";
      case "Account":
        return "text-indigo-700 bg-indigo-100 border-indigo-200";
      default:
        return "text-gray-700 bg-gray-100 border-gray-200";
    }
  };

  const handleTicketSelection = (ticketId) => {
    setSelectedTickets((prev) =>
      prev.includes(ticketId)
        ? prev.filter((id) => id !== ticketId)
        : [...prev, ticketId]
    );
  };

  const handleBulkApprove = () => {
    console.log("Bulk approving tickets:", selectedTickets);
    setSelectedTickets([]);
  };

  const handleBulkReject = () => {
    console.log("Bulk rejecting tickets:", selectedTickets);
    setSelectedTickets([]);
  };

  const handleTicketClick = (ticket) => {
    setSelectedTicket(ticket);
    setShowApprovalModal(true);
  };

  const handleApprove = (ticketId, approvalNotes) => {
    console.log("Approving ticket:", ticketId, "with notes:", approvalNotes);
    // Here you would typically make an API call to approve the ticket
    // Update ticket status to "Approved" and add approval notes
  };

  const handleReject = (ticketId, rejectionReason) => {
    console.log("Rejecting ticket:", ticketId, "with reason:", rejectionReason);
    // Here you would typically make an API call to reject the ticket
    // Update ticket status to "Rejected" and add rejection reason
  };

  // Get view statistics
  const getViewStats = () => {
    const allTickets = getTicketsByView("all");
    const unapprovedTickets = getTicketsByView("unapproved");
    const myTickets = getTicketsByView("myTickets");

    return {
      all: allTickets.length,
      unapproved: unapprovedTickets.length,
      myTickets: myTickets.length,
    };
  };

  const viewStats = getViewStats();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-3">
            <div>
              <h1 className="text-xl font-bold text-gray-900">
                Ticket Approval Center
              </h1>
              <p className="text-sm text-gray-600">
                Review and approve customer support requests
              </p>
            </div>
            <div className="flex space-x-2">
              {selectedTickets.length > 0 && (
                <>
                  <button
                    onClick={handleBulkApprove}
                    className="flex items-center px-3 py-1.5 text-sm bg-green-600 text-white rounded-md hover:bg-green-700"
                  >
                    <CheckCircle className="w-3 h-3 mr-1" />
                    Approve ({selectedTickets.length})
                  </button>
                  <button
                    onClick={handleBulkReject}
                    className="flex items-center px-3 py-1.5 text-sm bg-red-600 text-white rounded-md hover:bg-red-700"
                  >
                    <XCircle className="w-3 h-3 mr-1" />
                    Reject ({selectedTickets.length})
                  </button>
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
        {/* View Tabs */}
        <div className="bg-white rounded-lg shadow-sm mb-3">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-4">
              {[
                {
                  key: "all",
                  label: "All Tickets",
                  count: viewStats.all,
                  icon: List,
                },
                {
                  key: "unapproved",
                  label: "Unapproved Tickets",
                  count: viewStats.unapproved,
                  icon: Clock,
                },
                {
                  key: "myTickets",
                  label: "My Tickets",
                  count: viewStats.myTickets,
                  icon: UserCheck,
                },
              ].map((view) => {
                const Icon = view.icon;
                return (
                  <button
                    key={view.key}
                    onClick={() => setActiveView(view.key)}
                    className={`flex items-center space-x-2 py-3 px-1 border-b-2 font-medium text-sm transition-colors ${
                      activeView === view.key
                        ? "border-blue-500 text-blue-600"
                        : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    <span>{view.label}</span>
                    <span
                      className={`px-2 py-0.5 text-xs rounded-full ${
                        activeView === view.key
                          ? "bg-blue-100 text-blue-600"
                          : "bg-gray-100 text-gray-600"
                      }`}
                    >
                      {view.count}
                    </span>
                  </button>
                );
              })}
            </nav>
          </div>
        </div>

        {/* Multi-Select Filter */}
        <div className="mb-3">
          <MultiSelectFilter
            filterOptions={filterOptions}
            onApplyFilters={handleApplyFilters}
            onClearFilters={handleClearFilters}
            appliedFilters={appliedFilters[activeView] || {}}
          />
        </div>

        {/* Tickets Table */}
        <div className="bg-white rounded-lg shadow-sm">
          <div className="px-3 py-2 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-base font-medium text-gray-900">
                {activeView === "all" && "All Tickets"}
                {activeView === "unapproved" && "Unapproved Tickets"}
                {activeView === "myTickets" && "My Tickets"} (
                {currentViewTickets.length})
              </h3>
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  onChange={(e) => {
                    if (e.target.checked) {
                      setSelectedTickets(currentViewTickets.map((t) => t.id));
                    } else {
                      setSelectedTickets([]);
                    }
                  }}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <label className="text-xs text-gray-600">Select All</label>
              </div>
            </div>
          </div>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Select
                  </th>
                  <th className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Ticket Details
                  </th>
                  <th className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Customer
                  </th>
                  <th className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Category
                  </th>
                  <th className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Priority
                  </th>
                  <th className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Risk Level
                  </th>
                  <th className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Created
                  </th>
                  <th className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {currentViewTickets.map((ticket) => (
                  <tr
                    key={ticket.id}
                    className="hover:bg-blue-50 cursor-pointer transition-colors"
                    onClick={(e) => {
                      // Don't trigger row click if clicking on checkbox or action buttons
                      if (
                        e.target.type !== "checkbox" &&
                        !e.target.closest("button")
                      ) {
                        handleTicketClick(ticket);
                      }
                    }}
                  >
                    <td className="px-2 py-2 whitespace-nowrap">
                      <input
                        type="checkbox"
                        checked={selectedTickets.includes(ticket.id)}
                        onChange={() => handleTicketSelection(ticket.id)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        onClick={(e) => e.stopPropagation()}
                      />
                    </td>
                    <td className="px-2 py-2">
                      <div>
                        <div className="text-xs font-medium text-gray-900">
                          {ticket.id}
                        </div>
                        <div className="text-xs text-gray-600">
                          {ticket.subject}
                        </div>
                        {ticket.attachments > 0 && (
                          <div className="text-xs text-gray-400 flex items-center mt-0.5">
                            <span className="flex items-center">
                              📎 {ticket.attachments} attachment
                              {ticket.attachments > 1 ? "s" : ""}
                            </span>
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="px-2 py-2 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-6 w-6">
                          <div className="h-6 w-6 rounded-full bg-gray-300 flex items-center justify-center">
                            <Users className="w-3 h-3 text-gray-600" />
                          </div>
                        </div>
                        <div className="ml-2">
                          <div className="text-xs font-medium text-gray-900">
                            {ticket.customer}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-2 py-2 whitespace-nowrap">
                      <span
                        className={`px-2 py-1 text-xs font-medium rounded-md border ${getCategoryColor(
                          ticket.category
                        )}`}
                      >
                        {ticket.category}
                      </span>
                    </td>
                    <td className="px-2 py-2 whitespace-nowrap">
                      <div className="flex items-center">
                        <div
                          className={`w-1.5 h-1.5 rounded-full mr-1 ${getPriorityColor(
                            ticket.priority
                          )}`}
                        ></div>
                        <span className="text-xs text-gray-900">
                          {ticket.priority}
                        </span>
                        {ticket.priority === "Critical" && (
                          <ArrowUp className="w-3 h-3 ml-1 text-red-500" />
                        )}
                      </div>
                    </td>
                    <td className="px-2 py-2 whitespace-nowrap">
                      <span
                        className={`px-1.5 py-0.5 text-xs font-medium rounded-full ${getRiskColor(
                          ticket.riskLevel
                        )}`}
                      >
                        {ticket.riskLevel}
                      </span>
                    </td>
                    <td className="px-2 py-2 whitespace-nowrap text-xs text-gray-500">
                      <div className="flex items-center">
                        <Calendar className="w-3 h-3 mr-1" />
                        {ticket.created}
                      </div>
                    </td>
                    <td className="px-2 py-2 whitespace-nowrap text-xs font-medium">
                      <div className="flex space-x-1">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleTicketClick(ticket);
                          }}
                          className="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-100 transition-colors"
                          title="View Details"
                        >
                          <Eye className="w-3 h-3" />
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleApprove(ticket.id, "");
                          }}
                          className="text-green-600 hover:text-green-900 p-1 rounded hover:bg-green-100 transition-colors"
                          title="Quick Approve"
                        >
                          <CheckCircle className="w-3 h-3" />
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            // For quick reject, we'll need a reason, so open the modal
                            handleTicketClick(ticket);
                          }}
                          className="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-100 transition-colors"
                          title="Reject (with reason)"
                        >
                          <XCircle className="w-3 h-3" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Approval Modal */}
      {showApprovalModal && (
        <SuperUserTicketDetailModal
          ticket={selectedTicket}
          isOpen={showApprovalModal}
          onClose={() => {
            setShowApprovalModal(false);
            setSelectedTicket(null);
          }}
          onApprove={handleApprove}
          onReject={handleReject}
        />
      )}
    </div>
  );
};

export default SuperUserDashboard;
