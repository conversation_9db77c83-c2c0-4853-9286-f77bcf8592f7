import React, { useState, useRef, useEffect } from 'react';
import { Filter, X, Calendar, Search } from 'lucide-react';

const FilterDropdown = ({ 
  column, 
  options, 
  value, 
  onChange, 
  onClear, 
  type = "select" // select, multiselect, date, search
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const dropdownRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const filteredOptions = options?.filter(option =>
    option.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  const handleOptionSelect = (option) => {
    onChange(column, option);
    setIsOpen(false);
  };

  const handleClear = () => {
    onClear(column);
    setIsOpen(false);
  };

  const renderSelectFilter = () => (
    <div className="p-3 w-64">
      <div className="flex items-center justify-between mb-2">
        <h4 className="text-sm font-medium text-gray-900">Filter by {column}</h4>
        {value && (
          <button
            onClick={handleClear}
            className="text-xs text-red-600 hover:text-red-800 flex items-center"
          >
            <X className="w-3 h-3 mr-1" />
            Clear
          </button>
        )}
      </div>
      
      {options && options.length > 5 && (
        <div className="mb-2">
          <input
            type="text"
            placeholder="Search..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
          />
        </div>
      )}
      
      <div className="max-h-48 overflow-y-auto">
        {filteredOptions.map((option) => (
          <button
            key={option}
            onClick={() => handleOptionSelect(option)}
            className={`w-full text-left px-2 py-1.5 text-xs rounded hover:bg-gray-100 ${
              value === option ? 'bg-blue-100 text-blue-800' : 'text-gray-700'
            }`}
          >
            {option}
          </button>
        ))}
      </div>
    </div>
  );

  const renderDateFilter = () => (
    <div className="p-3 w-72">
      <div className="flex items-center justify-between mb-2">
        <h4 className="text-sm font-medium text-gray-900">Filter by Date Range</h4>
        {(value?.from || value?.to) && (
          <button
            onClick={handleClear}
            className="text-xs text-red-600 hover:text-red-800 flex items-center"
          >
            <X className="w-3 h-3 mr-1" />
            Clear
          </button>
        )}
      </div>
      
      <div className="space-y-2">
        <div>
          <label className="block text-xs text-gray-600 mb-1">From:</label>
          <input
            type="date"
            value={value?.from || ''}
            onChange={(e) => onChange(column, { ...value, from: e.target.value })}
            className="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
          />
        </div>
        <div>
          <label className="block text-xs text-gray-600 mb-1">To:</label>
          <input
            type="date"
            value={value?.to || ''}
            onChange={(e) => onChange(column, { ...value, to: e.target.value })}
            className="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
          />
        </div>
      </div>
    </div>
  );

  const renderSearchFilter = () => (
    <div className="p-3 w-64">
      <div className="flex items-center justify-between mb-2">
        <h4 className="text-sm font-medium text-gray-900">Search {column}</h4>
        {value && (
          <button
            onClick={handleClear}
            className="text-xs text-red-600 hover:text-red-800 flex items-center"
          >
            <X className="w-3 h-3 mr-1" />
            Clear
          </button>
        )}
      </div>
      
      <input
        type="text"
        placeholder={`Search ${column.toLowerCase()}...`}
        value={value || ''}
        onChange={(e) => onChange(column, e.target.value)}
        className="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
        autoFocus
      />
    </div>
  );

  const getFilterContent = () => {
    switch (type) {
      case 'date':
        return renderDateFilter();
      case 'search':
        return renderSearchFilter();
      default:
        return renderSelectFilter();
    }
  };

  return (
    <div className="relative inline-block" ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`p-1 rounded hover:bg-gray-100 transition-colors ${
          value ? 'text-blue-600' : 'text-gray-400'
        }`}
        title={`Filter ${column}`}
      >
        <Filter className="w-3 h-3" />
      </button>

      {isOpen && (
        <div className="absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
          {getFilterContent()}
        </div>
      )}
    </div>
  );
};

export default FilterDropdown;
