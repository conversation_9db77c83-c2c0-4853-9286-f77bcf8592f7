import React, { useState } from 'react';
import MultiSelectFilter from '../superuser/MultiSelectFilter';

const FilterDemo = () => {
  const [appliedFilters, setAppliedFilters] = useState({});

  const demoFilterOptions = {
    category: ['Technical', 'Feature Request', 'Security', 'Documentation', 'Billing', 'Account'],
    priority: ['Low', 'Medium', 'High', 'Critical'],
    riskLevel: ['Low', 'Medium', 'High'],
    status: ['Pending Approval', 'Approved', 'Rejected'],
    customer: ['TechCorp Ltd', 'DataSync Inc', 'SecureApp Solutions', 'DevTeam Alpha'],
    attachmentCount: ['0', '1', '2+']
  };

  const handleApplyFilters = (filters) => {
    console.log('Applied filters:', filters);
    setAppliedFilters(filters);
  };

  const handleClearFilters = () => {
    console.log('Cleared all filters');
    setAppliedFilters({});
  };

  return (
    <div className="p-8 bg-gray-50 min-h-screen">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold text-gray-900 mb-6">Multi-Select Filter Demo</h1>
        
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Filter Component</h2>
          
          <MultiSelectFilter
            filterOptions={demoFilterOptions}
            onApplyFilters={handleApplyFilters}
            onClearFilters={handleClearFilters}
            appliedFilters={appliedFilters}
          />
          
          <div className="mt-8">
            <h3 className="text-md font-medium text-gray-900 mb-3">Current Applied Filters:</h3>
            <pre className="bg-gray-100 p-4 rounded-md text-sm overflow-auto">
              {JSON.stringify(appliedFilters, null, 2)}
            </pre>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FilterDemo;
