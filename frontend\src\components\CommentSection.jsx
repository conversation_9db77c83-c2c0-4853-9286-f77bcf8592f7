import { MessageSquare, Paperclip, Send, Smile } from "lucide-react";
import { useEffect, useState } from "react";

const CommentSection = ({ ticketId, canComment }) => {
  const [comments, setComments] = useState([]);
  const [newMessage, setNewMessage] = useState("");
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [fileError, setFileError] = useState("");

  // Sample comments data - replace with actual API call
  useEffect(() => {
    const sampleComments = [
      {
        id: 1,
        author: "<PERSON>",
        role: "Customer",
        content:
          "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad",
        timestamp: "3 days ago",
        isCustomer: true,
        attachments: [],
        avatar: "D",
        avatarColor: "bg-teal-500",
      },
      {
        id: 2,
        author: "UI/UX Designer Member",
        role: "Agent",
        content:
          "Here are the design mockups for the new feature. Please review and let me know your thoughts.",
        timestamp: "3 days ago",
        isCustomer: false,
        attachments: [
          {
            name: "design-mockups.pdf",
            size: 2048576, // 2MB
            type: "application/pdf",
          },
          {
            name: "wireframes.png",
            size: 1024000, // 1MB
            type: "image/png",
          },
        ],
        avatar: "U",
        avatarColor: "bg-teal-500",
      },
      {
        id: 3,
        author: "Agent",
        role: "Agent",
        content:
          "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad",
        timestamp: "3 days ago",
        isCustomer: false,
        attachments: [],
        avatar: "M",
        avatarColor: "bg-gray-500",
      },
    ];
    setComments(sampleComments);
  }, [ticketId]);

  const handleFileSelect = (event) => {
    const files = Array.from(event.target.files);
    setFileError("");

    // Validate files
    const validFiles = [];
    const maxSize = 5 * 1024 * 1024; // 5MB in bytes
    const allowedTypes = [
      "application/pdf",
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      "text/plain",
      "application/vnd.ms-excel",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      "image/jpeg",
      "image/png",
      "image/gif",
    ];

    for (const file of files) {
      if (file.size > maxSize) {
        setFileError(`File "${file.name}" is too large. Maximum size is 5MB.`);
        return;
      }
      if (!allowedTypes.includes(file.type)) {
        setFileError(`File "${file.name}" is not a supported format.`);
        return;
      }
      validFiles.push(file);
    }

    setSelectedFiles([...selectedFiles, ...validFiles]);
    // Clear the input so the same file can be selected again
    event.target.value = "";
  };

  const removeFile = (index) => {
    setSelectedFiles(selectedFiles.filter((_, i) => i !== index));
    setFileError("");
  };

  const handleSendMessage = () => {
    if (!newMessage.trim() && selectedFiles.length === 0) return;

    const newComment = {
      id: comments.length + 1,
      author: "You",
      role: "Customer",
      content: newMessage || "Sent attachments",
      timestamp: "Just now",
      isCustomer: true,
      attachments: selectedFiles.map((file) => ({
        name: file.name,
        size: file.size,
        type: file.type,
      })),
      avatar: "Y",
      avatarColor: "bg-blue-500",
    };

    setComments([...comments, newComment]);
    setNewMessage("");
    setSelectedFiles([]);
    setFileError("");
  };

  const handleKeyPress = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const getFileIcon = (fileType) => {
    if (fileType.includes("pdf")) return "📄";
    if (fileType.includes("word") || fileType.includes("document")) return "📝";
    if (fileType.includes("excel") || fileType.includes("sheet")) return "📊";
    if (fileType.includes("image")) return "🖼️";
    if (fileType.includes("text")) return "📄";
    return "📎";
  };

  if (comments.length === 0) {
    return (
      <div className="flex flex-col h-96">
        <div className="flex-1 flex items-center justify-center text-gray-500">
          <div className="text-center">
            <MessageSquare className="w-12 h-12 mx-auto mb-3 text-gray-300" />
            <p>
              No messages yet.{" "}
              {canComment
                ? "Start the conversation!"
                : "Messages will appear here once the ticket is approved."}
            </p>
          </div>
        </div>
        {canComment && (
          <div className="border-t p-4 bg-white">
            {/* File Error Message */}
            {fileError && (
              <div className="mb-3 p-2 bg-red-50 border border-red-200 rounded-md">
                <p className="text-xs text-red-600">{fileError}</p>
              </div>
            )}

            {/* Selected Files Preview */}
            {selectedFiles.length > 0 && (
              <div className="mb-3 space-y-2">
                <p className="text-xs font-medium text-gray-700">
                  Selected files:
                </p>
                {selectedFiles.map((file, index) => (
                  <div
                    key={index}
                    className="flex items-center space-x-2 p-2 bg-blue-50 border border-blue-200 rounded-md"
                  >
                    <span className="text-sm">{getFileIcon(file.type)}</span>
                    <div className="flex-1 min-w-0">
                      <p className="text-xs font-medium text-gray-900 truncate">
                        {file.name}
                      </p>
                      <p className="text-xs text-gray-500">
                        {formatFileSize(file.size)}
                      </p>
                    </div>
                    <button
                      onClick={() => removeFile(index)}
                      className="p-1 text-red-500 hover:text-red-700 transition-colors"
                    >
                      <span className="text-sm">✕</span>
                    </button>
                  </div>
                ))}
              </div>
            )}

            <div className="flex items-end space-x-3">
              <div className="flex items-center space-x-2">
                <label className="p-2 text-gray-400 hover:text-teal-600 transition-colors rounded-lg hover:bg-teal-50 cursor-pointer">
                  <Paperclip className="w-4 h-4" />
                  <input
                    type="file"
                    multiple
                    accept=".pdf,.doc,.docx,.txt,.xls,.xlsx,.jpg,.jpeg,.png,.gif"
                    onChange={handleFileSelect}
                    className="hidden"
                  />
                </label>
              </div>
              <div className="flex-1">
                <textarea
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Type your message here..."
                  className="w-full p-3 border border-gray-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  rows="2"
                />
              </div>
              <button
                onClick={handleSendMessage}
                disabled={!newMessage.trim() && selectedFiles.length === 0}
                className="bg-teal-500 text-white p-3 rounded-lg hover:bg-teal-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
              >
                <Send className="w-5 h-5" />
              </button>
            </div>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full bg-gradient-to-b from-gray-50 to-gray-100">
      {/* Chat Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-3">
        {comments.map((comment) => (
          <div
            key={comment.id}
            className={`flex ${
              comment.isCustomer ? "justify-end" : "justify-start"
            }`}
          >
            <div
              className={`flex max-w-xs lg:max-w-md ${
                comment.isCustomer ? "flex-row-reverse" : "flex-row"
              } space-x-2`}
            >
              {/* Avatar */}
              <div
                className={`w-8 h-8 rounded-full ${
                  comment.avatarColor
                } flex items-center justify-center text-white text-sm font-medium flex-shrink-0 ${
                  comment.isCustomer ? "ml-2" : "mr-2"
                }`}
              >
                {comment.avatar}
              </div>

              {/* Message Bubble */}
              <div className="flex flex-col">
                {/* Author and timestamp */}
                <div
                  className={`text-xs text-gray-500 mb-1 ${
                    comment.isCustomer ? "text-right" : "text-left"
                  }`}
                >
                  <span className="font-medium">{comment.author}</span>
                </div>

                {/* Message content */}
                <div
                  className={`rounded-lg px-4 py-3 shadow-sm ${
                    comment.isCustomer
                      ? "bg-gradient-to-r from-teal-500 to-cyan-600 text-white"
                      : "bg-white border border-gray-200 text-gray-900 shadow-md"
                  }`}
                >
                  <p className="text-sm leading-relaxed">{comment.content}</p>

                  {/* Attachments */}
                  {comment.attachments && comment.attachments.length > 0 && (
                    <div className="mt-2 space-y-1">
                      {comment.attachments.map((attachment, index) => (
                        <div
                          key={index}
                          className={`flex items-center space-x-2 p-2 rounded-md ${
                            comment.isCustomer
                              ? "bg-white bg-opacity-20"
                              : "bg-gray-50"
                          }`}
                        >
                          <span className="text-sm">
                            {getFileIcon(attachment.type)}
                          </span>
                          <div className="flex-1 min-w-0">
                            <p
                              className={`text-xs font-medium truncate ${
                                comment.isCustomer
                                  ? "text-white"
                                  : "text-gray-900"
                              }`}
                            >
                              {attachment.name}
                            </p>
                            <p
                              className={`text-xs ${
                                comment.isCustomer
                                  ? "text-white text-opacity-80"
                                  : "text-gray-500"
                              }`}
                            >
                              {formatFileSize(attachment.size)}
                            </p>
                          </div>
                          <button
                            className={`p-1 rounded hover:bg-opacity-20 hover:bg-white transition-colors ${
                              comment.isCustomer
                                ? "text-white"
                                : "text-gray-600"
                            }`}
                          >
                            <span className="text-xs">📥</span>
                          </button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {/* Timestamp */}
                <div
                  className={`text-xs text-gray-400 mt-1 ${
                    comment.isCustomer ? "text-right" : "text-left"
                  }`}
                >
                  {comment.timestamp}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Chat Input */}
      {canComment && (
        <div className="border-t border-gray-200 p-4 bg-gradient-to-r from-white to-gray-50">
          {/* File Error Message */}
          {fileError && (
            <div className="mb-3 p-2 bg-red-50 border border-red-200 rounded-md">
              <p className="text-xs text-red-600">{fileError}</p>
            </div>
          )}

          {/* Selected Files Preview */}
          {selectedFiles.length > 0 && (
            <div className="mb-3 space-y-2">
              <p className="text-xs font-medium text-gray-700">
                Selected files:
              </p>
              {selectedFiles.map((file, index) => (
                <div
                  key={index}
                  className="flex items-center space-x-2 p-2 bg-blue-50 border border-blue-200 rounded-md"
                >
                  <span className="text-sm">{getFileIcon(file.type)}</span>
                  <div className="flex-1 min-w-0">
                    <p className="text-xs font-medium text-gray-900 truncate">
                      {file.name}
                    </p>
                    <p className="text-xs text-gray-500">
                      {formatFileSize(file.size)}
                    </p>
                  </div>
                  <button
                    onClick={() => removeFile(index)}
                    className="p-1 text-red-500 hover:text-red-700 transition-colors"
                  >
                    <span className="text-sm">✕</span>
                  </button>
                </div>
              ))}
            </div>
          )}

          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2">
              <label className="p-2 text-gray-400 hover:text-teal-600 transition-colors rounded-lg hover:bg-teal-50 cursor-pointer">
                <Paperclip className="w-4 h-4" />
                <input
                  type="file"
                  multiple
                  accept=".pdf,.doc,.docx,.txt,.xls,.xlsx,.jpg,.jpeg,.png,.gif"
                  onChange={handleFileSelect}
                  className="hidden"
                />
              </label>
              <button className="p-2 text-gray-400 hover:text-teal-600 transition-colors rounded-lg hover:bg-teal-50">
                <Smile className="w-4 h-4" />
              </button>
            </div>

            <div className="flex-1 flex items-center space-x-2">
              <input
                type="text"
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                onKeyDown={handleKeyPress}
                placeholder="Type your message here..."
                className="flex-1 p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500 bg-white shadow-sm transition-all duration-200"
              />
              <button
                onClick={handleSendMessage}
                disabled={!newMessage.trim() && selectedFiles.length === 0}
                className="bg-gradient-to-r from-teal-500 to-cyan-600 text-white p-3 rounded-lg hover:from-teal-600 hover:to-cyan-700 disabled:from-gray-300 disabled:to-gray-400 disabled:cursor-not-allowed transition-all duration-200 shadow-sm"
              >
                <Send className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CommentSection;
